package com.jdh.o2oservice.core.domain.support.securitynumber.rpc.bo;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description 录音调取
 * @Date 2024/12/19 下午1:17
 * <AUTHOR>
 **/
@Data
public class CallRecordingBO implements Serializable {

    /**
     * 业务单元id
     */
    private String buId;

    /**
     * 业务单据号
     */
    private Long orderId;

    /**
     * 通话唯一标识
     */
    private String callId;

    /**
     * 主叫号码
     */
    private String callNo;

    /**
     * 被叫虚拟号
     */
    private String secretNo;

    /**
     * 呼叫开始时间，格式 yyyy-MM-dd HH:mm:ss
     */
    private String startTime;

    /**
     * 呼叫结束时间，格式 yyyy-MM-dd HH:mm:ss
     */
    private String finishTime;

    /**
     * 通话持续时间，秒
     */
    private String callDuration;

    /**
     * 通话录音下载地址，有效期2小时
     */
    private String recordingFileDownloadUrl;

}
