package com.jdh.o2oservice.core.domain.support.medicalpromsie.dto;

import com.jdh.o2oservice.export.medicalpromise.dto.MedPromiseDeliveryStepDTO;
import lombok.Data;

import java.util.List;

/**
 * @ClassName:JdhMedicalPromiseQueryVo
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/6/24 11:05
 * @Vserion: 1.0
 **/
@Data
public class JdhMedicalPromiseQueryDto {

    /**
     * 实验室id
     */
    private String stationId;

    /**
     * 患者ID
     */
    private Long promisePatientId;

    /**
     * 实验室名称
     */
    private String stationName;

    /**
     * 实验室联系方式
     */
    private String stationPhone;

    /**
     * 服务地点详细地址
     */
    private String stationAddress;

    /**
     * 服务站id
     */
    private String angelStationId;

    /**
     * 检测项目id
     */
    private String serviceItemId;

    /**
     * 检测单配送步骤
     */
    private List<MedPromiseDeliveryStepDTO> deliveryStepFlow;

    private Long medicalPromiseId;//检测单id


}
