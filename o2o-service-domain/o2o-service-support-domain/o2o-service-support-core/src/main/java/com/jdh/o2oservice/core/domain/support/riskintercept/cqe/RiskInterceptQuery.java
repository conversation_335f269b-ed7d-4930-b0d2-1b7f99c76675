package com.jdh.o2oservice.core.domain.support.riskintercept.cqe;

import lombok.Data;

import java.util.List;

/**
 * @ClassName RiskInterceptQuery
 * @Description
 * <AUTHOR>
 * @Date 2025/7/9 22:06
 */
@Data
public class RiskInterceptQuery {

    /**
     * 履约单id
     */
    private Long promiseId;


    /**
     * 履约单患者id集合
     */
    private List<Long> promisePatientIds;

    /**
     * 风控编码
     */
    private String riskCode;

}
