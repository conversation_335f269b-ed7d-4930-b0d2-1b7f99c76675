package com.jdh.o2oservice.core.domain.support.quickcheckmedicine.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/12/18
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QuickCheckMedicineBo {
    /**
     * 唯一标识符，用于区分不同的药品检查请求。
     */
    private String uniqueSign;
    /**
     * 用户的个人识别码，用于验证用户身份。
     */
    private String userPin;
    /**
     * 患者ID
     */
    private Long patientId;
    /**
     * 履约单ID
     */
    private String performanceId;
    /**
     * 检测结果
     */
    private List<String> detectionResultList;
    /**
     * 存储药品检测结果的图片列表。
     */
    private List<String> detectionImgList;
    /**
     * 存储药品检测的详细信息列表。
     */
    private List<QuickCheckDrugBo> quickCheckDrugBos;
}
