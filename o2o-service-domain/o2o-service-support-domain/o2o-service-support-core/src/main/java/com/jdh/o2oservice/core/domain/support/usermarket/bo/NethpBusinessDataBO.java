package com.jdh.o2oservice.core.domain.support.usermarket.bo;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/1/6
 */
@Data
public class NethpBusinessDataBO {
    /**
     * 场景代码，用于标识业务数据所属的场景。
     */
    private String sceneCode;
    /**
     * 诊断订单时间，记录业务数据的诊断订单发生时间。
     */
    private String  diagOrderTime;
    /**
     * 存储业务数据相关的诊断报告列表。
     */
    private List<NethpDiagReportBO> diagReportDataList;
}
