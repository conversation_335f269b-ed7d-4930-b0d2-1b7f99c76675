package com.jdh.o2oservice.core.domain.support.riskintercept.cqe;

import lombok.Data;

import java.util.List;

/**
 * @ClassName RiskInterceptModifyStatusCmd
 * @Description
 * <AUTHOR>
 * @Date 2025/7/9 22:20
 */
@Data
public class RiskInterceptModifyStatusCmd {

    /**
     * 履约单id
     */
    private Long promiseId;


    /**
     * 履约单患者id集合
     */
    private List<Long> promisePatientIds;

    /**
     * 风控编码
     */
    private String riskCode;

    /**
     * 风控是否通过 1:通过，2:不通过
     */
    private Integer riskPassStatus;

}
