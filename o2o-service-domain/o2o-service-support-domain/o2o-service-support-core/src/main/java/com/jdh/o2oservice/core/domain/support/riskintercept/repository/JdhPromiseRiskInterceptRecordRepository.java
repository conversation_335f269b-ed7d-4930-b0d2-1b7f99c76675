package com.jdh.o2oservice.core.domain.support.riskintercept.repository;

import com.jdh.o2oservice.base.model.Repository;
import com.jdh.o2oservice.core.domain.support.riskintercept.cqe.RiskInterceptModifyStatusCmd;
import com.jdh.o2oservice.core.domain.support.riskintercept.cqe.RiskInterceptQuery;
import com.jdh.o2oservice.core.domain.support.riskintercept.model.JdhPromiseRiskInterceptRecord;
import com.jdh.o2oservice.core.domain.support.riskintercept.model.JdhPromiseRiskInterceptRecordIdentifier;

import java.util.List;

/**
 * @ClassName JdhPromiseRiskInterceptRecordRepository
 * @Description
 * <AUTHOR>
 * @Date 2025/7/9 16:57
 */
public interface JdhPromiseRiskInterceptRecordRepository extends Repository<JdhPromiseRiskInterceptRecord, JdhPromiseRiskInterceptRecordIdentifier> {

    /**
     * 批量保存风控记录
     *
     * @param interceptRecordList
     * @return
     */
    int batchSave(List<JdhPromiseRiskInterceptRecord> interceptRecordList);

    /**
     * 批量保存风控记录
     *
     * @param riskInterceptQuery
     * @return
     */
    List<JdhPromiseRiskInterceptRecord> queryRiskInterceptList(RiskInterceptQuery riskInterceptQuery);

    /**
     * 修改拦截记录状态
     *
     * @param riskInterceptModifyStatusCmd
     * @return
     */
    int modifyRiskInterceptStatus(RiskInterceptModifyStatusCmd riskInterceptModifyStatusCmd);

    int deleteRiskIntercept(JdhPromiseRiskInterceptRecord jdhPromiseRiskInterceptRecord);
}
