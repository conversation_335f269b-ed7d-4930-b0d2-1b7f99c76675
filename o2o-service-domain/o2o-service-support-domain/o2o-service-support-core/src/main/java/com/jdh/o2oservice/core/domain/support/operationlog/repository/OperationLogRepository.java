package com.jdh.o2oservice.core.domain.support.operationlog.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.base.model.Repository;
import com.jdh.o2oservice.core.domain.support.operationlog.model.OperationLog;
import com.jdh.o2oservice.core.domain.support.operationlog.model.OperationLogIdentifier;

import java.util.List;

/**
 * 操作日志仓储层
 */
public interface OperationLogRepository extends Repository<OperationLog, OperationLogIdentifier> {

    /**
     * 分页查询
     * @param operateLog operateLog
     * @return page
     */
    Page<OperationLog> queryPage(OperationLog operateLog);

    /**
     * 批量插入数据
     *
     * @param operationLogList operationLogList
     */
    Integer batchInsert(List<OperationLog> operationLogList);
}
