package com.jdh.o2oservice.core.domain.support.operationlog.context;

import lombok.Data;

import java.util.List;

/**
 * 业务主键上下文
 *
 * <AUTHOR>
 * @date 2023-10-07 11:01
 */
@Data
public class OperationLogContext {
    /**
     * 登录信息上下文
     */
    private String bizId;

    /**
     * 权限信息上下文
     */
    private List<String> bizIdList;

    /**
     * 用户信息
     */
    private static final ThreadLocal<OperationLogContext> BIZ_HOLDER = new ThreadLocal<>();

    /**
     * 将业务主键放到上下文中
     *
     * @param operationLogContext operationLogContext
     */
    public static void put(OperationLogContext operationLogContext) {
        BIZ_HOLDER.set(operationLogContext);
    }

    /**
     * 将业务主键放到上下文中
     *
     * @param bizId bizId
     */
    public static void putBizId(String bizId) {
        OperationLogContext operationLogContext = get();
        if (operationLogContext == null) {
            operationLogContext = new OperationLogContext();
        }
        operationLogContext.setBizId(bizId);
        put(operationLogContext);
    }

    /**
     * 将业务主键放到上下文中
     *
     * @param bizId bizId
     */
    public static void putBizIdList(List<String> bizId) {
        OperationLogContext operationLogContext = get();
        if (operationLogContext == null) {
            operationLogContext = new OperationLogContext();
        }
        operationLogContext.setBizIdList(bizId);
        put(operationLogContext);
    }

    /**
     * 从ThreadLocal获取
     *
     * @return UserContext
     */
    public static OperationLogContext get() {
        return BIZ_HOLDER.get();
    }

    /**
     * 将userPin从ThreadLocal移除
     */
    public static void remove() {
        BIZ_HOLDER.remove();
    }
}
