package com.jdh.o2oservice.core.domain.support.securitynumber.rpc;
import com.jdh.o2oservice.core.domain.support.securitynumber.rpc.bo.SecurityNumberBindAxbResultBO;
import com.jdh.o2oservice.core.domain.support.securitynumber.rpc.bo.SecurityNumberBindRecordBO;
import com.jdh.o2oservice.core.domain.support.securitynumber.rpc.bo.SecurityNumberCallRecordBO;
import com.jdh.o2oservice.core.domain.support.securitynumber.rpc.param.SecurityNumberBindAxbParam;
import com.jdh.o2oservice.core.domain.support.securitynumber.rpc.param.SecurityNumberReleaseRpcParam;

import java.util.List;

/**
 * @Description 虚拟号服务
 * @Date 2024/12/19 上午10:45
 * <AUTHOR>
 **/
public interface ISecurityNumberServiceRpc {

    /**
     * 虚拟号绑定
     * @param param
     * @return
     */
    SecurityNumberBindAxbResultBO bindAxb(SecurityNumberBindAxbParam param);

    /**
     * 虚拟号解绑
     * @param param
     * @return
     */
    Boolean release(SecurityNumberReleaseRpcParam param);

    /**
     * 录音调取
     * @param buId
     * @param orderId
     * @return
     */
    SecurityNumberCallRecordBO queryCallRecord(String buId, Long orderId);

    /**
     * 根据订单号查询当前在绑的虚拟号
     * @param orderId
     * @return
     */
    List<SecurityNumberBindRecordBO> queryOnBindingRecordByOrderId(Long orderId);
}
