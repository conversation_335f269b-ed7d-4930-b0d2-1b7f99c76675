package com.jdh.o2oservice.core.domain.support.riskintercept.model;

import com.jdh.o2oservice.base.model.Identifier;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName JdhPromiseRiskInterceptRecordIdentifier
 * @Description
 * <AUTHOR>
 * @Date 2025/7/9 17:01
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class JdhPromiseRiskInterceptRecordIdentifier implements Identifier {

    /** 触达通道配置的账号ID，全局唯一，是一堆配置信息的标识 */
    private Long recordId;

    /**
     * 序列化
     *
     * @return {@link String}
     */
    @Override
    public String serialize() {
        return String.valueOf(recordId);
    }
}
