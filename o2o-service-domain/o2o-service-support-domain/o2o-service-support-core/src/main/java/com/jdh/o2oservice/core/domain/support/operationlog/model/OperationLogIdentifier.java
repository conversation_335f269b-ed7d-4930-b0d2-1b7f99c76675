package com.jdh.o2oservice.core.domain.support.operationlog.model;

import com.jdh.o2oservice.base.model.Identifier;
import lombok.Builder;
import lombok.Data;

/**
 * 操作日志唯一
 *
 * <AUTHOR>
 * @date 2025/01/16
 */
@Data
@Builder
public class OperationLogIdentifier implements Identifier {
    /**
     * 主键id
     */
    private Long id;
    @Override
    public String serialize() {
        return String.valueOf(id);
    }
}
