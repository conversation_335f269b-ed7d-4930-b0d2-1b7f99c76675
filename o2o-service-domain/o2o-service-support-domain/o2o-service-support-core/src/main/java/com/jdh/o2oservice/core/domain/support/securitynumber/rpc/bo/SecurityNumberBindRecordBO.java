package com.jdh.o2oservice.core.domain.support.securitynumber.rpc.bo;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description 当前在绑的虚拟号
 * @Date 2024/12/19 下午1:27
 * <AUTHOR>
 **/
@Data
public class SecurityNumberBindRecordBO implements Serializable {

    /**
     * 场景类型 正向订单业务：forward 逆向售后业务：backward 等，由平台分配
     */
    private String sceneType;

    /**
     * 业务类型 pop、dropship 等，由平台分配
     */
    private String businessType;

    /**
     * 调用端平台编码 shop端、VC端 等，由平台分配
     */
    private String platformCode;

    /**
     * 商家id
     */
    private String venderId;

    /**
     * 订单号
     */
    private Long orderId;

    /**
     * 计费标识，逆向场景必传
     */
    private String chargingId;

    private Integer bindSource;

    private String poolSN;

    /**
     * 绑定关系id
     */
    private String bindId;

    /**
     * 虚拟号
     */
    private String secretNo;

    /**
     * 虚拟号分机号
     */
    private String extension;

    private Date createTime;

    /**
     * 绑定关系有效期
     */
    private Date expiration;

    /**
     * 明文手机号
     */
    private String phoneNo;

    private Integer tc;

    private Integer dc;
}
