package com.jdh.o2oservice.core.domain.support.usermarket.repository;

import com.jdh.o2oservice.core.domain.support.usermarket.bo.UserMarketQueryBO;
import com.jdh.o2oservice.core.domain.support.usermarket.context.UserMarketContext;
import com.jdh.o2oservice.core.domain.support.usermarket.model.UserMarket;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/12/6
 */
public interface UserMarketRepository {

    /**
     * 保存用户营销信息。
     * @param userMarketContext 用户营销上下文信息。
     * @return 保存操作是否成功。
     */
    Boolean saveUserMarketInfo(UserMarketContext userMarketContext);


    /**
     * 根据查询条件获取用户营销信息
     * @param userMarketQueryBO 用户营销信息查询对象
     * @return 用户营销信息
     */
    List<UserMarket> queryUserMarketList(UserMarketQueryBO userMarketQueryBO);
}
