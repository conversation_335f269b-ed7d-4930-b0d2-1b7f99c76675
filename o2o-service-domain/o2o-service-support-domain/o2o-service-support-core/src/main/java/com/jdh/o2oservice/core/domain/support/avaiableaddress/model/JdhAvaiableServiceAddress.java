package com.jdh.o2oservice.core.domain.support.avaiableaddress.model;

import com.jdh.o2oservice.base.model.Entity;
import lombok.Data;

import java.util.Date;

/**
 * 费项配置
 * @author: wangyu1387
 * @date: 2024/8/13 5:47 下午
 * @version: 1.0
 */
@Data
public class JdhAvaiableServiceAddress implements Entity<JdhAvaiableServiceAddressIdentifier> {
    /**
     * 主键id
     */
    private Long id;
    /**
     * 上传ID
     */
    private Long uploadId;
    /**
     * 类型，0：护士服务开通城市
     */
    private Integer type=0;

    /**
     * 省ID
     */
    private Integer provinceId;
    /**
     * 省名称
     */
    private String provinceName;
    /**
     * 市ID
     */
    private Integer cityId;
    /**
     * 市名称
     */
    private String cityName;
    /**
     * 区ID
     */
    private Integer countyId;
    /**
     * 区名称
     */
    private String countyName;
    /**
     * 镇ID
     */
    private Integer townId;
    /**
     * 镇名称
     */
    private String townName;

    /**
     * 创建人
     */
    private String createUser;
    /**
     * 修改人
     */
    private String updateUser;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改时间
     */
    private Date updateTime;

    private Integer yn;

    @Override
    public JdhAvaiableServiceAddressIdentifier getIdentifier() {
        return new JdhAvaiableServiceAddressIdentifier(id);
    }

    @Override
    public Integer version() {
        return 1;
    }

    @Override
    public void versionIncrease() {

    }
}
