package com.jdh.o2oservice.core.domain.support.usermarket.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/1/10
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class NethpRxSkuExtendBO {
    /**
     * 商店ID
     */
    private Long storeId;
    /**
     * 包含NethpRxSkuBO对象的列表，用于存储和管理与当前类相关的NethpRxSkuBO实例。
     */
    private List<NethpRxSkuBO> nethpRxSkuBOList;
}
