package com.jdh.o2oservice.core.domain.support.riskintercept.event;

import com.jdh.o2oservice.base.event.EventBody;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName RiskInterceptEventBody
 * @Description
 * <AUTHOR>
 * @Date 2025/7/9 21:05
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RiskInterceptEventBody implements EventBody {

    /**
     * 履约单id
     */
    private Long promiseId;

}
