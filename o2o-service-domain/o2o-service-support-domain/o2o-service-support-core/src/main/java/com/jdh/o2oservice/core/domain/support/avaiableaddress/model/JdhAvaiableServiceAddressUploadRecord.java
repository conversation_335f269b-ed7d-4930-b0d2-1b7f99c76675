package com.jdh.o2oservice.core.domain.support.avaiableaddress.model;

import com.jdh.o2oservice.base.model.Entity;
import lombok.Data;

import java.util.Date;

/**
 * 费项配置
 * @author: wangyu1387
 * @date: 2024/8/13 5:47 下午
 * @version: 1.0
 */
@Data
public class JdhAvaiableServiceAddressUploadRecord implements Entity<JdhAvaiableServiceAddressUploadRecordIdentifier> {
    /**
     * 主键id
     */
    private Long id;
    /**
     * 费项配置文件
     */
    private String url;
    /**
     * 费项配置文件名称
     */
    private String title;

    /**
     * 文件解析状态:1-解析中 2-解析成功 3-解析失败
     */
    private Integer status;

    /**
     * 创建人
     */
    private String createUser;
    /**
     * 修改人
     */
    private String updateUser;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改时间
     */
    private Date updateTime;
    /**
     * <pre>
     * 分页
     * </pre>
     */
    private int pageNum = 1;

    /**
     * <pre>
     * 页容量
     * </pre>
     */
    private int pageSize = 10;

    private String fileId;

    @Override
    public JdhAvaiableServiceAddressUploadRecordIdentifier getIdentifier() {
        return new JdhAvaiableServiceAddressUploadRecordIdentifier(id);
    }

    @Override
    public Integer version() {
        return 1;
    }

    @Override
    public void versionIncrease() {

    }
}
