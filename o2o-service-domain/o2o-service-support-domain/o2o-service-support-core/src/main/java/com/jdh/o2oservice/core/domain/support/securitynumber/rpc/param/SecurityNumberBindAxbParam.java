package com.jdh.o2oservice.core.domain.support.securitynumber.rpc.param;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description 虚拟号绑定
 * @Date 2024/12/19 上午11:32
 * <AUTHOR>
 **/
@Data
public class SecurityNumberBindAxbParam implements Serializable {

    /**
     * 场景类型 正向订单业务：forward 逆向售后业务：backward 等，由平台分配
     */
    private String sceneType;

    /**
     * 调用端平台编码 shop端、VC端 等，由平台分配
     */
    private String platformCode;

    /**
     * 业务类型 pop、dropship 等，由平台分配
     */
    private String businessType;

    /**
     * 业务单元id，pop业务传venderId，厂直业务传venderCode（包含字母）
     */
    private String buId;

    /**
     * 业务单据号，正向场景传订单号，逆向场景传服务单号
     */
    private Long orderId;

    /**
     * 计费标识，逆向场景必传
     */
    private String chargingId;

    /**
     * 承运商编号（发货场景使用）
     */
    private String providerCode;

    /**
     * 运单号（发货场景使用）
     */
    private String shipId;

    /**
     *
     */
    private String poolSN;

    /**
     * AX中的A号码。可设置为手机号码或固定电话，固定电话需要加区号，区号和号码中间不需要加连字符，例如031177992688。
     */
    private String phoneNoA;

    /**
     * AXB中的B号码。可设置为手机号码或固定电话，固定电话需要加区号，区号和号码中间不需要加连字符，例如031177992688。
     */
    private String phoneNoB;

    /**
     * 绑定关系的过期时间。必须晚于当前时间1分钟以上。日期格式："yyyy-MM-dd HH:mm:ss"
     */
    private String expiration;

    private Integer callDisplayType;

    /**
     * 虚拟号分机号
     */
    private String extension;

    /**
     * 指定城市进行X号码的选号。如果当前号池中没有该城市的可用号码，或未指定此参数，
     * 将从当前号码池中随机分配一个其他城市的号码作为X号码。如果配置了严格模式，则不存在符合条件的号码时会提示分配错误。
     */
    private String expectCity;

    /**
     * 指定城市进行X号码的选号。如果当前号池中没有该城市的可用号码，或未指定此参数，将从当前号码池中随机分配一个其他城市的号码作为X号码。
     * 如果配置了严格模式，则不存在符合条件的号码时会提示分配错误。 该参数与phoneNoX是二选一关系。
     */
    private String phoneNoX;

    /**
     * 用户自定义数据
     */
    private String userData;
}
