package com.jdh.o2oservice.core.domain.support.quickcheckmedicine.rpc;

import com.jdh.o2oservice.core.domain.support.quickcheckmedicine.bo.AddCartResultBo;
import com.jdh.o2oservice.core.domain.support.quickcheckmedicine.bo.QuickCheckMedicineBo;
import com.jdh.o2oservice.core.domain.support.quickcheckmedicine.bo.QuickCheckResultBo;
import com.jdh.o2oservice.core.domain.support.quickcheckmedicine.bo.RxNewPrescriptAddCartBo;

/**
 * <AUTHOR>
 * @description
 * @date 2024/12/18
 */
public interface QuickCheckMedicineRpc {


    /**
     * 一键购药跳转链接方法
     * @param quickCheckMedicineBo 快速检查药品信息对象
     * @return 快速检查结果对象
     */
    QuickCheckResultBo quickCheckJumpLink(QuickCheckMedicineBo quickCheckMedicineBo);


    /**
     * 添加处方药到购物车
     * @param rxNewPrescriptAddCartBo 处方药添加到购物车的请求参数对象
     * @return 添加结果
     */
    AddCartResultBo addCartForPrescriptV2(RxNewPrescriptAddCartBo rxNewPrescriptAddCartBo);

}
