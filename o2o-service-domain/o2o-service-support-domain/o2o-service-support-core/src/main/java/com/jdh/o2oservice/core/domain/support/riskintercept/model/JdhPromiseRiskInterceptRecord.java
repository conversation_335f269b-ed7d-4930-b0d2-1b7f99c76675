package com.jdh.o2oservice.core.domain.support.riskintercept.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jdh.o2oservice.base.model.*;
import com.jdh.o2oservice.core.domain.support.basic.enums.SupportAggregateEnum;
import com.jdh.o2oservice.core.domain.support.reach.model.JdhReachChannelAccountIdentifier;
import com.jdh.o2oservice.core.domain.support.riskintercept.bo.RiskExtBo;
import lombok.Data;

import java.util.Date;

/**
 * @ClassName JdhPromiseRiskInterceptRecordPo
 * @Description
 * <AUTHOR>
 * @Date 2025/07/09 15:20
 **/
@Data
public class JdhPromiseRiskInterceptRecord implements Aggregate<JdhPromiseRiskInterceptRecordIdentifier> {

    /**
     * 主键
     */
    private Long id;

    /**
     * 业务流水号
     */
    private Long recordId;

    /**
     * 领域编码
     */
    private String interceptDomainCode;

    /**
     * 聚合根编码
     */
    private String interceptAggregateCode;

    /**
     * 订单Id
     */
    private String orderId;

    /**
     * 履约单id
     */
    private Long promiseId;

    /**
     * 履约单患者id
     */
    private Long promisePatientId;

    /**
     * 拦截事件编码
     */
    private String interceptEventCode;

    /**
     * 拦截事件消费者
     */
    private String interceptEventConsumer;

    /**
     * 风控编码
     */
    private String riskCode;

    /**
     * 是否要执行风控 1:执行，2:不执行
     */
    private Integer riskWorkTag;

    /**
     * 风控是否通过 1:通过，2:不通过
     */
    private Integer riskPassStatus;

    /**
     * 扩展信息
     */
    private RiskExtBo riskExtBo;

    /**
     * 是否有效 0：无效；1：有效
     */
    private Integer yn;

    /**
     * 是否有效 0：无效；1：有效
     */
    private Integer version;

    /**
     * 数据来源分支
     */
    private String branch;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 版本
     *
     * @return {@link Integer}
     */
    @Override
    public Integer version() {
        return this.version;
    }

    /**
     * 版本增加
     */
    @Override
    public void versionIncrease() {
        this.version++;
    }

    /**
     * 获取标识符
     *
     * @return {@link ID}
     */
    @Override
    public JdhPromiseRiskInterceptRecordIdentifier getIdentifier() {
        return new JdhPromiseRiskInterceptRecordIdentifier(this.recordId);
    }
    @Override
    public DomainCode getDomainCode() {
        return DomainEnum.BASE;
    }

    @Override
    public AggregateCode getAggregateCode() {
        return SupportAggregateEnum.RISK_INTERCEPT;
    }
}