package com.jdh.o2oservice.core.domain.support.riskintercept.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * @ClassName RiskWorkTagEnum
 * @Description
 * <AUTHOR>
 * @Date 2025/7/9 17:58
 */
@Getter
@AllArgsConstructor
public enum RiskWorkTagEnum {

    /** */
    NEED_TAG(1, "需要执行"),

    NEED_NO_TAG(2, "不需要执行"),

    ;
    private Integer tag;

    private String tagDesc;

    public static boolean needDo(Integer tag) {
        if(Objects.isNull(tag)) {
            return false;
        }
        return RiskWorkTagEnum.NEED_TAG.getTag().equals(tag);
    }
}
