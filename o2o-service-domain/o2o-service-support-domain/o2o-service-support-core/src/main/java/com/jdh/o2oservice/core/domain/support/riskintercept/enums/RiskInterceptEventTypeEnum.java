package com.jdh.o2oservice.core.domain.support.riskintercept.enums;

import com.jdh.o2oservice.base.event.EventType;
import com.jdh.o2oservice.base.model.AggregateCode;
import com.jdh.o2oservice.core.domain.support.basic.enums.SupportAggregateEnum;
import com.jdh.o2oservice.core.domain.support.riskintercept.event.RiskInterceptEventBody;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 风险评估单事件
 * @date 2025-07-04 18:11
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum RiskInterceptEventTypeEnum implements EventType {

    /**
     * 高风险项目评估
     */
    RISK_INTERCEPT_BEGIN(SupportAggregateEnum.RISK_INTERCEPT,"riskInterceptBegin","风险拦截开始", RiskInterceptEventBody.class),

    HIGH_RISK_ITEM_INTERCEPT(SupportAggregateEnum.RISK_INTERCEPT,"promiseHighRiskItemIntercept","高风险项目评估", RiskInterceptEventBody.class),

    HIGH_RISK_ITEM_PASS(SupportAggregateEnum.RISK_INTERCEPT,"highRiskItemPass","高风险项目评估通过", RiskInterceptEventBody.class),

    HIGH_RISK_ITEM_FAIL(SupportAggregateEnum.RISK_INTERCEPT,"highRiskItemFail","高风险项目评估不通过", RiskInterceptEventBody.class),

    RISK_INTERCEPT_END(SupportAggregateEnum.RISK_INTERCEPT,"riskInterceptEnd","风险拦截结束", RiskInterceptEventBody.class),

    ;
    private SupportAggregateEnum aggregateCode;

    private String code;

    private String desc;

    private Class<?> bodyClass;

    /**
     * 事件所属的领域
     */
    @Override
    public AggregateCode getAggregateType() {
        return aggregateCode;
    }

    /**
     * 事件编码
     */
    @Override
    public String getCode() {
        return code;
    }

    /**
     * 事件描述
     */
    @Override
    public String getDesc() {
        return desc;
    }

    /**
     * @return
     */
    @Override
    public Class<?> bodyClass() {
        return bodyClass;
    }

}
