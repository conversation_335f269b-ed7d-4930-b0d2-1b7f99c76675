package com.jdh.o2oservice.core.domain.support.quickcheckmedicine.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description
 * @date 2024/12/18
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QuickCheckDrugBo {
    /**
     * 药品的通用名称。
     */
    private String genericName;
    /**
     * 药品的规格信息。
     */
    private String specification;
    /**
     * 药品的数量。
     */
    private Integer num;
}
