package com.jdh.o2oservice.core.domain.support.riskintercept.context;

import lombok.Data;

import java.util.List;

/**
 * @ClassName JdhPromiseRiskInterceptContext
 * @Description
 * <AUTHOR>
 * @Date 2025/7/9 17:29
 */
@Data
public class JdhPromiseRiskInterceptContext {

    /**
     * 领域编码
     */
    private String domainCode;

    /**
     * 聚合根编码
     */
    private String aggregateCode;

    /**
     * 订单id
     */
    private String orderId;

    /**
     * 履约单id
     */
    private Long promiseId;

    /**
     * 拦截事件编码
     */
    private String interceptEventCode;

    /**
     * 风控编码
     */
    private String riskCode;

    /**
     * 是否要执行风控 1:执行，2:不执行
     */
    private Integer riskWorkTag;

    /**
     * 风控是否通过 1:通过，2:不通过
     */
    private Integer riskPassStatus;

    /**
     * 履约单患者id集合
     */
    private List<Long> promisePatientIds;

    /**
     * 事件消息体
     */
    private String eventBody;
}
