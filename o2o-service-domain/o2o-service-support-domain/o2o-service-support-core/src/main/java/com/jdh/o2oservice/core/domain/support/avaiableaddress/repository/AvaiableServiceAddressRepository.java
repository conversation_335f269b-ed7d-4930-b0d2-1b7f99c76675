package com.jdh.o2oservice.core.domain.support.avaiableaddress.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.base.model.Repository;
import com.jdh.o2oservice.core.domain.support.avaiableaddress.model.JdhAvaiableServiceAddress;
import com.jdh.o2oservice.core.domain.support.avaiableaddress.model.JdhAvaiableServiceAddressIdentifier;
import com.jdh.o2oservice.core.domain.support.avaiableaddress.model.JdhAvaiableServiceAddressUploadRecord;
import com.jdh.o2oservice.core.domain.support.avaiableaddress.model.JdhAvaiableServiceAddressUploadRecordIdentifier;
import com.jdh.o2oservice.core.domain.support.feeConfig.model.FeeConfig;

import java.util.List;

/**
 * 费项配置仓储层
 *
 * @author: wangyu1387
 * @date: 2024/4/26 8:43 下午
 * @version: 1.0
 */
public interface AvaiableServiceAddressRepository extends Repository<JdhAvaiableServiceAddress, JdhAvaiableServiceAddressIdentifier> {

    List<JdhAvaiableServiceAddress> queryList();

    void batchSave(Long uploadId, List<JdhAvaiableServiceAddress> entites);

}
