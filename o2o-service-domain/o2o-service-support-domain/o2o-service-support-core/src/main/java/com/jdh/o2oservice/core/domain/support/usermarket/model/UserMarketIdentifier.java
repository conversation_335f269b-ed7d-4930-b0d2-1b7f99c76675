package com.jdh.o2oservice.core.domain.support.usermarket.model;

import com.jdh.o2oservice.base.model.Identifier;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2024/12/6
 */
@Data
@Builder
public class UserMarketIdentifier implements Identifier {

    /**
     * 唯一ID。
     */
    private Long id;
    /**
     * 序列化
     *
     * @return {@link String}
     */
    @Override
    public String serialize() {
        return String.valueOf(id);
    }
}
