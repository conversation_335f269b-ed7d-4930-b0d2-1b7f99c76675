package com.jdh.o2oservice.core.domain.support.riskintercept.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @ClassName RiskPassStatusEnum
 * @Description
 * <AUTHOR>
 * @Date 2025/7/9 18:19
 */
@Getter
@AllArgsConstructor
public enum RiskPassStatusEnum {

    /** */
    INIT(-1, "初始状态"),
    
    PASS(1, "通过"),

    NO_PASS(2, "不通过"),

    INVALID(3, "作废"),
    ;
    private Integer status;

    private String desc;
}
