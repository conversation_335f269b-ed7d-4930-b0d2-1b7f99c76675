package com.jdh.o2oservice.core.domain.support.medicalpromsie.param;

import lombok.Data;

import java.util.List;

/**
 * @ClassName:MedicalPromiseBo
 * @Description:
 * @Author: yaoqing<PERSON>
 * @Date: 2024/6/21 16:43
 * @Vserion: 1.0
 **/
@Data
public class MedicalPromiseParam {

    /**
     * 履约单ID
     */
    private Long promiseId;

    /**
     * 订单号
     */
    private Long orderId;

    /**
     * 预约天 yyyy-MM-dd
     */
    private String scheduleDay;

    /**
     * 预约时间 09:00-10:00
     */
    private String bookTimeSpan;

    /**
     * 派送起始地址
     */
    private String startAddress;

    /**
     * 履约人唯一IDList
     */
    private List<Long> promisePatientIdList;

}
