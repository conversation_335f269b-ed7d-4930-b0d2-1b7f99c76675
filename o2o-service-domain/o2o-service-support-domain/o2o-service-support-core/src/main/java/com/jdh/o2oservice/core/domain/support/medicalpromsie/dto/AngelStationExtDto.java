package com.jdh.o2oservice.core.domain.support.medicalpromsie.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @ClassName AngelStationExtDto
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/9/25 23:37
 */
@Data
public class AngelStationExtDto {

    /**
     * 服务站id
     */
    private Long angelStationId;

    /**
     * 围栏id
     */
    private String fenceId;

    /**
     * 服务站名称
     */
    private String angelStationName;

    /**
     * 地图id
     */
    private Long mapId;

    /**
     * 图层id
     */
    private Long layerId;

    /**
     * 范围的衡量类型（1：公里数，2：分钟）
     */
    private Integer fenceRangeType;

    /**
     * 范围的衡量值（如xx公里或xx分钟）
     */
    private Integer fenceRangeRadius;

    /**
     * 圆中心点纬度
     */
    private String fenceRangeCenterLat;

    /**
     * 圆中心点经度
     */
    private String fenceRangeCenterLng;

    /**
     * 站长erp
     */
    private String stationMaster;

    /**
     * 省编码
     */
    private String provinceCode;

    /**
     * 省名称
     */
    private String provinceName;

    /**
     * 市编码
     */
    private String cityCode;

    /**
     * 市名称
     */
    private String cityName;

    /**
     * 区编码
     */
    private String districtCode;

    /**
     * 区名称
     */
    private String districtName;

    /**
     * 县编码
     */
    private String countyCode;

    /**
     * 县名称
     */
    private String countyName;

    /**
     * 详细地址
     */
    private String addressDetail;

    /**
     * 全地址
     */
    private String fullAddress;

    /**
     * 站点状态：1启用 2停用
     */
    private Integer stationStatus;

    /**
     * 开始营业时间(24小时)
     */
    private String openHour;

    /**
     * 停止营业时间(24小时)
     */
    private String closeHour;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String updateUser;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 围栏形状 空+1:圆形 2多边形
     */
    private Integer fenceShapeType;

    /**
     * 服务资源类型 二进制编码 右向左 1位骑手 2位护士
     */
    private List<Integer> angelTypes;

    /**
     * 骑手供应商 服务资源类型 二进制编码 右向左 1位达达 2位顺丰
     */
    private List<Integer> deliverySuppliers;

    /**
     * 实验室id
     */
    private String stationId;

    /**
     * 实验室名称
     */
    private String stationName;

    /**
     * 服务资源数量
     */
    private Integer angelNum;

}
