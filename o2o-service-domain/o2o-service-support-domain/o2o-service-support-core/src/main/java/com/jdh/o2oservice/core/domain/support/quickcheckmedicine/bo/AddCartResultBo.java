package com.jdh.o2oservice.core.domain.support.quickcheckmedicine.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2024/12/20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AddCartResultBo {

    /**
     * 添加到购物车的操作结果。
     */
    private Boolean success;

    /**
     * 业务流程字段
     * 百亿补贴 key：hasTenBillionSubsidySku value：true
     * 百亿补贴隐藏店铺加车标识,key:bybt value:1
     * 预售，key:isPresale value:true
     */
    private Map<String,String> businessMap;
}
