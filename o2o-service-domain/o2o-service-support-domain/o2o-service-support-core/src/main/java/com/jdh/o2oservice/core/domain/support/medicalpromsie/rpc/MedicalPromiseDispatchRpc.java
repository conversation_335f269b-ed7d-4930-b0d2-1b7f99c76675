package com.jdh.o2oservice.core.domain.support.medicalpromsie.rpc;


import com.jdh.o2oservice.core.domain.support.medicalpromsie.dto.AngelStationExtDto;
import com.jdh.o2oservice.core.domain.support.medicalpromsie.dto.JdhMedicalPromiseQueryDto;
import com.jdh.o2oservice.core.domain.support.medicalpromsie.param.AngelStationExtQueryParam;
import com.jdh.o2oservice.core.domain.support.medicalpromsie.param.MedicalPromiseParam;
import com.jdh.o2oservice.core.domain.support.medicalpromsie.param.MedicalPromiseQueryParam;

import java.util.List;

/**
 * @InterfaceName:MedicalPromiseDispatchRpc
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/6/21 16:40
 * @Vserion: 1.0
 **/
public interface MedicalPromiseDispatchRpc {

    /**
     * 派实验室
     *
     * @param medicalPromiseParam
     * @return
     */
    Boolean dispatchStation(MedicalPromiseParam medicalPromiseParam);

    /**
     * 查询实验室
     *
     * @param medicalPromiseQueryParam
     */
    List<JdhMedicalPromiseQueryDto> queryMedicalPromiseList(MedicalPromiseQueryParam medicalPromiseQueryParam);

    /**
     * 查询服务站明细
     *
     * @param angelStationExtQueryParam
     * @return
     */
    AngelStationExtDto queryAngelStationDetail(AngelStationExtQueryParam angelStationExtQueryParam);
}
