package com.jdh.o2oservice.core.domain.support.riskintercept.factory;

import com.google.common.collect.Lists;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.base.util.SpringUtil;
import com.jdh.o2oservice.core.domain.support.riskintercept.bo.RiskExtBo;
import com.jdh.o2oservice.core.domain.support.riskintercept.context.JdhPromiseRiskInterceptContext;
import com.jdh.o2oservice.core.domain.support.riskintercept.enums.RiskPassStatusEnum;
import com.jdh.o2oservice.core.domain.support.riskintercept.model.JdhPromiseRiskInterceptRecord;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @ClassName JdhPromiseRiskInterceptRecordFactory
 * @Description
 * <AUTHOR>
 * @Date 2025/7/9 17:25
 */
public class JdhPromiseRiskInterceptRecordFactory {

    /**
     * 创建风控拦截单
     *
     * @param jdhPromiseRiskInterceptContext
     * @return
     */
    public static List<JdhPromiseRiskInterceptRecord> create(JdhPromiseRiskInterceptContext jdhPromiseRiskInterceptContext) {
        List<JdhPromiseRiskInterceptRecord> interceptRecordList = Lists.newArrayList();

        List<Long> promisePatientIds = jdhPromiseRiskInterceptContext.getPromisePatientIds();
        for (Long promisePatientId : promisePatientIds) {
            JdhPromiseRiskInterceptRecord record = new JdhPromiseRiskInterceptRecord();
            record.setRecordId(SpringUtil.getBean(GenerateIdFactory.class).getId());
            record.setInterceptDomainCode(jdhPromiseRiskInterceptContext.getDomainCode());
            record.setInterceptAggregateCode(jdhPromiseRiskInterceptContext.getAggregateCode());
            record.setOrderId(jdhPromiseRiskInterceptContext.getOrderId());
            record.setPromiseId(jdhPromiseRiskInterceptContext.getPromiseId());
            record.setPromisePatientId(promisePatientId);
            record.setInterceptEventCode(jdhPromiseRiskInterceptContext.getInterceptEventCode());
            record.setRiskCode(jdhPromiseRiskInterceptContext.getRiskCode());
            record.setRiskWorkTag(jdhPromiseRiskInterceptContext.getRiskWorkTag());
            record.setRiskPassStatus(Objects.isNull(jdhPromiseRiskInterceptContext.getRiskPassStatus()) ? RiskPassStatusEnum.INIT.getStatus() : jdhPromiseRiskInterceptContext.getRiskPassStatus());
            record.setBranch(SpringUtil.getProperty("spring.profiles.active"));
            record.setCreateTime(new Date());
            record.setUpdateTime(new Date());

            if(StringUtils.isNotBlank(jdhPromiseRiskInterceptContext.getEventBody())) {
                RiskExtBo riskExtBo = new RiskExtBo();
                riskExtBo.setEventBody(jdhPromiseRiskInterceptContext.getEventBody());
                record.setRiskExtBo(riskExtBo);
            }

            interceptRecordList.add(record);
        }
        return interceptRecordList;
    }

}
