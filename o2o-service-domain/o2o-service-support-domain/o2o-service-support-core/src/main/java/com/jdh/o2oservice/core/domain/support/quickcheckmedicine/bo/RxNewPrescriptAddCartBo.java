package com.jdh.o2oservice.core.domain.support.quickcheckmedicine.bo;

import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2024/12/20
 */
@Data
public class RxNewPrescriptAddCartBo {
    /**
     * 用户pin
     */
    private String pin;
    /**
     * 处方id
     */
    private String prescriptId;
    /**
     * 添加购物车渠道(本次互医入参传2)
     * 1:处方单主购物车加车
     * 2:互联网医院处方立即购买加车
     */
    private Integer addCartChannel;
    /**
     * "multiAddCart":"1"
     */
    private Map<String,String> flags;

    /**
     * agent
     */
    private String agent;
}
