package com.jdh.o2oservice.core.domain.support.riskintercept.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * @ClassName RiskInterceptCodeEnum
 * @Description
 * <AUTHOR>
 * @Date 2025/7/9 17:19
 */
@Getter
@AllArgsConstructor
@Slf4j
public enum RiskInterceptCodeEnum {

    /** */
    RISK_HIGH_ITEM_INTERCEPT("highRiskItem", "高风险项目拦截"),

    ;


    private String riskCode;

    private String riskDesc;
}
