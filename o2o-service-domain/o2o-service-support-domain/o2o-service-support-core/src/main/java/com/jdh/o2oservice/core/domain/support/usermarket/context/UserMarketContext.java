package com.jdh.o2oservice.core.domain.support.usermarket.context;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2024/12/6
 */
@Data
public class UserMarketContext {
    /**
     * 垂直业务身份编码
     */
    private String verticalCode;

    /**
     * 服务类型
     */
    private String serviceType;

    /**
     * 用户pin
     */
    private String userPin;

    /**
     * 履约单ID
     */
    private Long promiseId;

    /**
     * 用户唯一ID
     */
    private Long promisePatientId;

    /**
     * 检测单状态
     */
    private Long patientId;

    /**
     * 场景，例：报告页用药建议
     */
    private String scene;

    /**
     * 营销类型，例：处方单
     */
    private String marketType;

    /**
     * 营销信息，例：处方单类型时存放处方ID
     */
    private String marketDetail;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String updateUser;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 扩展字段
     */
    private String extendInfo;
}
