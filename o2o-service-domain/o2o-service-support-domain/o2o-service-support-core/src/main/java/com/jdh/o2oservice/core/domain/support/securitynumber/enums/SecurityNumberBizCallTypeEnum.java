package com.jdh.o2oservice.core.domain.support.securitynumber.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description 通话类型
 * @Date 2024/12/20 上午11:02
 * <AUTHOR>
 **/
@Getter
@AllArgsConstructor
public enum SecurityNumberBizCallTypeEnum {

    APPOINTMENT_TO_ANGEL(1, "预约人打给服务者"),

    SERVICED_TO_ANGEL(2, "被服务人打给服务者"),

    ANGEL_TO_APPOINTMENT(3, "服务者打给预约人"),

    ANGEL_TO_SERVICED(4, "服务者打给被服务人"),
    ;

    private Integer code;

    private String desc;
}
